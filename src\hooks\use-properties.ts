'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';
import type { Database } from '@/types/database';

type Property = Database['public']['Tables']['properties']['Row'];
type PropertyInsert = Database['public']['Tables']['properties']['Insert'];
type PropertyUpdate = Database['public']['Tables']['properties']['Update'];

interface UsePropertiesOptions {
  limit?: number;
  offset?: number;
  status?: string;
  propertyType?: string;
  city?: string;
  minPrice?: number;
  maxPrice?: number;
  searchQuery?: string;
}

interface UsePropertiesReturn {
  properties: Property[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  refetch: () => Promise<void>;
  createProperty: (property: PropertyInsert) => Promise<Property | null>;
  updateProperty: (id: string, updates: PropertyUpdate) => Promise<Property | null>;
  deleteProperty: (id: string) => Promise<boolean>;
}

export function useProperties(options: UsePropertiesOptions = {}): UsePropertiesReturn {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const {
    limit = 10,
    offset = 0,
    status,
    propertyType,
    city,
    minPrice,
    maxPrice,
    searchQuery
  } = options;

  const fetchProperties = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('properties')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (status && status !== 'all') {
        query = query.eq('status', status);
      }

      if (propertyType && propertyType !== 'all') {
        query = query.eq('property_type', propertyType);
      }

      if (city && city !== 'all') {
        query = query.eq('city', city);
      }

      if (minPrice) {
        query = query.gte('price', minPrice);
      }

      if (maxPrice) {
        query = query.lte('price', maxPrice);
      }

      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,address.ilike.%${searchQuery}%`);
      }

      const { data, error: fetchError, count } = await query;

      if (fetchError) {
        throw fetchError;
      }

      setProperties(data || []);
      setTotalCount(count || 0);
    } catch (err) {
      console.error('Error fetching properties:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch properties');
    } finally {
      setLoading(false);
    }
  };

  const createProperty = async (property: PropertyInsert): Promise<Property | null> => {
    try {
      setError(null);
      const { data, error: createError } = await supabase
        .from('properties')
        .insert(property)
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      // Refresh the list
      await fetchProperties();
      return data;
    } catch (err) {
      console.error('Error creating property:', err);
      setError(err instanceof Error ? err.message : 'Failed to create property');
      return null;
    }
  };

  const updateProperty = async (id: string, updates: PropertyUpdate): Promise<Property | null> => {
    try {
      setError(null);
      const { data, error: updateError } = await supabase
        .from('properties')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setProperties(prev => prev.map(p => p.id === id ? data : p));
      return data;
    } catch (err) {
      console.error('Error updating property:', err);
      setError(err instanceof Error ? err.message : 'Failed to update property');
      return null;
    }
  };

  const deleteProperty = async (id: string): Promise<boolean> => {
    try {
      setError(null);
      const { error: deleteError } = await supabase
        .from('properties')
        .delete()
        .eq('id', id);

      if (deleteError) {
        throw deleteError;
      }

      // Update local state
      setProperties(prev => prev.filter(p => p.id !== id));
      setTotalCount(prev => prev - 1);
      return true;
    } catch (err) {
      console.error('Error deleting property:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete property');
      return false;
    }
  };

  const refetch = async () => {
    await fetchProperties();
  };

  useEffect(() => {
    fetchProperties();
  }, [limit, offset, status, propertyType, city, minPrice, maxPrice, searchQuery]);

  return {
    properties,
    loading,
    error,
    totalCount,
    refetch,
    createProperty,
    updateProperty,
    deleteProperty,
  };
}

// Hook for single property
export function useProperty(id: string) {
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;

    const fetchProperty = async () => {
      try {
        setLoading(true);
        setError(null);

        const { data, error: fetchError } = await supabase
          .from('properties')
          .select('*')
          .eq('id', id)
          .single();

        if (fetchError) {
          throw fetchError;
        }

        setProperty(data);
      } catch (err) {
        console.error('Error fetching property:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch property');
      } finally {
        setLoading(false);
      }
    };

    fetchProperty();
  }, [id]);

  return { property, loading, error };
}
