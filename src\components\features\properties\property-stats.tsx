import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { usePropertyStats } from '@/hooks/use-property-stats';
import { Building, Eye, MessageSquare, TrendingUp, Loader2, AlertCircle } from 'lucide-react';

export function PropertyStats() {
  const { stats, loading, error } = usePropertyStats();

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-center">
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center text-red-500">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>Error loading stats: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const statItems = [
    {
      title: 'Total Properties',
      value: stats.totalProperties.toString(),
      change: `${stats.recentListings} new this month`,
      icon: Building,
      color: 'text-blue-600',
    },
    {
      title: 'Available',
      value: stats.availableProperties.toString(),
      change: `${Math.round((stats.availableProperties / stats.totalProperties) * 100)}% of total`,
      icon: Eye,
      color: 'text-green-600',
    },
    {
      title: 'Sold/Rented',
      value: (stats.soldProperties + stats.rentedProperties).toString(),
      change: `${stats.pendingProperties} pending`,
      icon: MessageSquare,
      color: 'text-purple-600',
    },
    {
      title: 'Avg. Price',
      value: `${(stats.averagePrice / 1000000).toFixed(1)}M MAD`,
      change: `Total: ${(stats.totalValue / 1000000).toFixed(1)}M MAD`,
      icon: TrendingUp,
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-gray-600 mt-1">{stat.change}</p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
