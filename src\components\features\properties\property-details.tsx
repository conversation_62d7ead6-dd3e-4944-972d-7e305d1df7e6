'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useProperty } from '@/hooks/use-properties';
import { Bed, Bath, Square, MapPin, Calendar, User, Check, Loader2, AlertCircle } from 'lucide-react';

interface PropertyDetailsProps {
  propertyId: string;
}

export function PropertyDetails({ propertyId }: PropertyDetailsProps) {
  const { property, loading, error } = useProperty(propertyId);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-gray-600">Loading property details...</span>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Property Not Found</h3>
          <p className="text-gray-600">{error || 'The requested property could not be found.'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 mt-6">
      {/* Basic Info */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="success">{property.status}</Badge>
          <Badge variant="secondary">{property.property_type}</Badge>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {property.title}
        </h1>
        <div className="flex items-center text-gray-600 mb-4">
          <MapPin className="h-5 w-5 mr-2" />
          <span>{property.address || `${property.city}, ${property.region}`}</span>
        </div>
        <div className="text-3xl font-bold text-primary mb-6">
          {property.price.toLocaleString()} {property.currency}
        </div>
      </div>

      {/* Key Features */}
      <Card>
        <CardHeader>
          <CardTitle>Property Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {property.bedrooms && (
              <div className="text-center">
                <Bed className="h-8 w-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold">{property.bedrooms}</div>
                <div className="text-sm text-gray-600">Bedrooms</div>
              </div>
            )}
            {property.bathrooms && (
              <div className="text-center">
                <Bath className="h-8 w-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold">{property.bathrooms}</div>
                <div className="text-sm text-gray-600">Bathrooms</div>
              </div>
            )}
            {property.area && (
              <div className="text-center">
                <Square className="h-8 w-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold">{property.area}</div>
                <div className="text-sm text-gray-600">m² Area</div>
              </div>
            )}
            <div className="text-center">
              <Calendar className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{new Date(property.created_at).getFullYear()}</div>
              <div className="text-sm text-gray-600">Listed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Description */}
      <Card>
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed">
            {property.description}
          </p>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Features & Amenities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {property.features && property.features.length > 0 ? (
              property.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Check className="h-4 w-4 text-primary" />
                  <span className="text-sm">{feature}</span>
                </div>
              ))
            ) : (
              <p className="text-gray-600 col-span-full">No features listed for this property.</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Location */}
      <Card>
        <CardHeader>
          <CardTitle>Location</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center text-gray-400 mb-4">
            <div className="text-center">
              <MapPin className="h-12 w-12 mx-auto mb-2" />
              <p>Interactive Map</p>
            </div>
          </div>
          <p className="text-gray-600">
            Located in {property.city}, {property.region}, {property.country}.
            {property.address && ` Address: ${property.address}`}
          </p>
        </CardContent>
      </Card>

      {/* Contact Info */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-primary" />
            </div>
            <div>
              <div className="font-semibold">Darden Property & Management</div>
              <div className="text-sm text-gray-600">+212 5XX XXX XXX</div>
              <div className="text-sm text-gray-600"><EMAIL></div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
