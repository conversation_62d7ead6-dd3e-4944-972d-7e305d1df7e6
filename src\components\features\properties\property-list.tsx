'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useProperties } from '@/hooks/use-properties';
import {
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Eye,
  Trash2,
  MapPin,
  Bed,
  Bath,
  Square,
  Loader2,
  AlertCircle
} from 'lucide-react';

export function PropertyList() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const {
    properties,
    loading,
    error,
    totalCount,
    refetch
  } = useProperties({
    searchQuery: searchQuery || undefined,
    status: selectedStatus !== 'all' ? selectedStatus : undefined,
    limit: 20
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'success';
      case 'sold': return 'destructive';
      case 'rented': return 'info';
      case 'pending': return 'warning';
      default: return 'secondary';
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Properties</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={refetch}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search properties..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="available">Available</option>
              <option value="sold">Sold</option>
              <option value="rented">Rented</option>
              <option value="pending">Pending</option>
            </select>

            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Loading properties...</span>
        </div>
      )}

      {/* Properties List */}
      {!loading && (
        <div className="space-y-4">
          {properties.length === 0 ? (
            <div className="text-center p-8">
              <p className="text-gray-600">No properties found matching your criteria.</p>
            </div>
          ) : (
            properties.map((property) => (
              <Card key={property.id} className="hover:shadow-lg transition-all duration-300 hover-lift fade-in scale-in">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {property.title}
                    </h3>
                    <Badge variant={getStatusColor(property.status) as any}>
                      {property.status}
                    </Badge>
                    <Badge variant="secondary">
                      {property.property_type}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center text-gray-600 mb-3">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span className="text-sm">{property.address || property.city}</span>
                  </div>

                  <div className="flex items-center space-x-6 text-sm text-gray-600 mb-3">
                    {property.bedrooms && (
                      <div className="flex items-center">
                        <Bed className="h-4 w-4 mr-1" />
                        <span>{property.bedrooms} beds</span>
                      </div>
                    )}
                    {property.bathrooms && (
                      <div className="flex items-center">
                        <Bath className="h-4 w-4 mr-1" />
                        <span>{property.bathrooms} baths</span>
                      </div>
                    )}
                    {property.area && (
                      <div className="flex items-center">
                        <Square className="h-4 w-4 mr-1" />
                        <span>{property.area} m²</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <span>Listed {new Date(property.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">
                      {property.price.toLocaleString()} {property.currency}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" className="hover:bg-primary hover:text-white transition-colors">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="hover:bg-italian-green hover:text-white transition-colors">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="hover:bg-gray-100 transition-colors">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
            ))
          )}
        </div>
      )}

      {/* Results Summary */}
      {!loading && properties.length > 0 && (
        <div className="text-center text-sm text-gray-600">
          Showing {properties.length} of {totalCount} properties
        </div>
      )}
    </div>
  );
}
